import express from "express";
import cors from "cors";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3005;

// API key stored securely on server side
const ANTIER_API_KEY =
  process.env.ANTIER_API_KEY || "5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k";

app.use(cors());
app.use(express.json());

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ status: "ok", timestamp: new Date().toISOString() });
});

// Widget serving endpoints
app.get("/widget.js", (req, res) => {
  const widgetPath = path.join(__dirname, "widget", "widget.js");
  res.setHeader("Content-Type", "application/javascript");
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.sendFile(widgetPath);
});

// Serve widget static files
app.use("/widget", express.static(path.join(__dirname, "widget")));

// Main chat endpoint that handles streaming
app.post("/ask", async (req, res) => {
  const { query, sessionId } = req.body;

  if (!query || typeof query !== "string") {
    return res.status(400).json({
      error: "Query is required and must be a string",
    });
  }

  try {
    // Set headers for streaming
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Headers", "Cache-Control");

    // Construct the external API URL with optional sessionId
    let externalApiUrl = `https://chatai.abstraxn.com/api/v1/chat/?apikey=${ANTIER_API_KEY}&query=You are Antier's Policy AI assistant. Now answer the question: ${encodeURIComponent(
      query
    )}&stream=true`;

    // Add sessionId to the external API URL if provided
    if (sessionId && typeof sessionId === "string") {
      externalApiUrl += `&sessionId=${encodeURIComponent(sessionId)}`;
    }

    console.log("🚀 Making request to external API for query:", query);
    console.log("🔗 Using session ID:", sessionId || "new session");

    // Make request to external API
    const response = await fetch(externalApiUrl, {
      method: "GET",
      headers: {
        Accept: "text/event-stream",
        "Cache-Control": "no-cache",
        Origin: "https://chatai.abstraxn.com",
        Referer: "https://chatai.abstraxn.com/",
        Host: "chatai.abstraxn.com",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    if (!response.ok) {
      let errorMessage = `External API error! status: ${response.status}`;
      let errorType = "api";

      if (response.status === 429) {
        errorMessage = "Rate limit exceeded. Please wait before trying again.";
        errorType = "rate_limit";
      } else if (response.status >= 500) {
        errorMessage =
          "External service is temporarily unavailable. Please try again later.";
        errorType = "server";
      } else if (response.status === 401 || response.status === 403) {
        errorMessage = "Authentication failed with external service.";
        errorType = "api";
      }

      throw new Error(`${errorType}:${errorMessage}`);
    }

    if (!response.body) {
      throw new Error("External API response body is null");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    // Handle client disconnect
    req.on("close", () => {
      console.log("Client disconnected, cleaning up...");
      reader.releaseLock();
    });

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.trim() && line.startsWith("data: ")) {
            // Forward the data directly to the client
            res.write(line + "\n");

            // Check if this is a done message
            const data = line.slice(6); // Remove 'data: ' prefix
            if (data.trim()) {
              try {
                const parsedData = JSON.parse(data);
                if (parsedData.type === "done") {
                  res.end();
                  return;
                }
              } catch {
                // Continue if we can't parse, let client handle it
              }
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
      res.end();
    }
  } catch (error) {
    console.error("Error in /ask endpoint:", error);

    let errorMessage = "Unknown error occurred";
    let errorType = "unknown";

    if (error instanceof Error) {
      if (error.message.includes(":")) {
        [errorType, errorMessage] = error.message.split(":", 2);
      } else {
        errorMessage = error.message;
      }

      // Additional error categorization
      if (
        error.message.includes("ECONNREFUSED") ||
        error.message.includes("ENOTFOUND")
      ) {
        errorType = "network";
        errorMessage =
          "Unable to connect to external service. Please try again later.";
      } else if (error.message.includes("timeout")) {
        errorType = "timeout";
        errorMessage = "Request timed out. Please try again.";
      }
    }

    // Send error as SSE format
    const errorResponse = {
      type: "error",
      error: errorMessage,
      errorType: errorType,
      timestamp: new Date().toISOString(),
    };

    res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
    res.end();
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`🔒 API key is securely stored on server side`);
});
