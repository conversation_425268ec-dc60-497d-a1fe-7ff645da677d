(function () {
  'use strict';

  // Widget configuration
  let config = {
    apiUrl: null, // Will be auto-detected
    theme: 'light',
    position: 'bottom-right',
    title: 'AI Assistant',
    apiKey: null,
    clientId: null
  };

  // Widget state
  let isOpen = false;
  let isInitialized = false;
  let sessionId = null;

  // Auto-detect API URL
  function getApiUrl() {
    if (config.apiUrl) {
      return config.apiUrl;
    }

    // Auto-detect based on current domain
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}`;
  }

  // Create isolated CSS
  const widgetStyles = `
    .aniter-widget-container {
      position: fixed;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
      color: #333;
      box-sizing: border-box;
    }

    .aniter-widget-container * {
      box-sizing: border-box;
    }

    .aniter-widget-button {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
    }

    .aniter-widget-button:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    .aniter-widget-chat {
      position: absolute;
      bottom: 80px;
      width: 350px;
      height: 500px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.3s ease;
    }

    .aniter-widget-chat.open {
      opacity: 1;
      transform: translateY(0);
    }

    .aniter-widget-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .aniter-widget-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .aniter-widget-close {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 20px;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .aniter-widget-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      background: #f8f9fa;
    }

    .aniter-widget-message {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
    }

    .aniter-widget-message.user {
      justify-content: flex-end;
    }

    .aniter-widget-message-content {
      max-width: 80%;
      padding: 12px 16px;
      border-radius: 18px;
      word-wrap: break-word;
    }

    .aniter-widget-message.user .aniter-widget-message-content {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-bottom-right-radius: 4px;
    }

    .aniter-widget-message.assistant .aniter-widget-message-content {
      background: white;
      color: #333;
      border: 1px solid #e1e5e9;
      border-bottom-left-radius: 4px;
    }

    .aniter-widget-input-container {
      padding: 16px;
      background: white;
      border-top: 1px solid #e1e5e9;
    }

    .aniter-widget-input-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .aniter-widget-input {
      flex: 1;
      border: 1px solid #e1e5e9;
      border-radius: 20px;
      padding: 10px 16px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.3s ease;
    }

    .aniter-widget-input:focus {
      border-color: #667eea;
    }

    .aniter-widget-send {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s ease;
    }

    .aniter-widget-send:hover {
      transform: scale(1.1);
    }

    .aniter-widget-send:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .aniter-widget-loading {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;
      font-size: 12px;
    }

    .aniter-widget-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .aniter-widget-error {
      background: #fee;
      color: #c33;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      margin-bottom: 8px;
    }

    /* Position variants */
    .aniter-widget-container.bottom-right {
      bottom: 20px;
      right: 20px;
    }

    .aniter-widget-container.bottom-left {
      bottom: 20px;
      left: 20px;
    }

    .aniter-widget-container.bottom-right .aniter-widget-chat {
      right: 0;
    }

    .aniter-widget-container.bottom-left .aniter-widget-chat {
      left: 0;
    }

    /* Dark theme */
    .aniter-widget-container.dark .aniter-widget-chat {
      background: #1a1a1a;
      color: #fff;
    }

    .aniter-widget-container.dark .aniter-widget-messages {
      background: #2a2a2a;
    }

    .aniter-widget-container.dark .aniter-widget-message.assistant .aniter-widget-message-content {
      background: #333;
      color: #fff;
      border-color: #444;
    }

    .aniter-widget-container.dark .aniter-widget-input-container {
      background: #1a1a1a;
      border-top-color: #444;
    }

    .aniter-widget-container.dark .aniter-widget-input {
      background: #333;
      color: #fff;
      border-color: #444;
    }

    .aniter-widget-container.dark .aniter-widget-input:focus {
      border-color: #667eea;
    }

    /* Mobile responsive */
    @media (max-width: 480px) {
      .aniter-widget-chat {
        width: calc(100vw - 40px);
        height: calc(100vh - 120px);
        bottom: 80px;
        left: 20px;
        right: 20px;
      }
    }
  `;

  // Inject styles
  function injectStyles() {
    if (document.getElementById('aniter-widget-styles')) return;

    const style = document.createElement('style');
    style.id = 'aniter-widget-styles';
    style.textContent = widgetStyles;
    document.head.appendChild(style);
  }

  // Create widget HTML
  function createWidgetHTML() {
    const container = document.createElement('div');
    container.className = `aniter-widget-container ${config.position} ${config.theme}`;
    container.innerHTML = `
      <div class="aniter-widget-chat">
        <div class="aniter-widget-header">
          <h3>${config.title}</h3>
          <button class="aniter-widget-close" onclick="AniterWidget.close()">×</button>
        </div>
        <div class="aniter-widget-messages" id="aniter-widget-messages"></div>
        <div class="aniter-widget-input-container">
          <div class="aniter-widget-input-wrapper">
            <input 
              type="text" 
              class="aniter-widget-input" 
              id="aniter-widget-input"
              placeholder="Type your message..."
              onkeypress="if(event.key==='Enter') AniterWidget.sendMessage()"
            >
            <button class="aniter-widget-send" id="aniter-widget-send" onclick="AniterWidget.sendMessage()">
              →
            </button>
          </div>
        </div>
      </div>
      <button class="aniter-widget-button" onclick="AniterWidget.toggle()">
        💬
      </button>
    `;

    return container;
  }

  // Simple markdown parser for basic formatting
  function parseMarkdown(text) {
    return text
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      // Headers
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      // Unordered lists
      .replace(/^\* (.*$)/gm, '<li>$1</li>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      // Line breaks
      .replace(/\n/g, '<br>')
      // Email addresses
      .replace(
        /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
        '<a href="mailto:$1" class="email-link">$1</a>'
      );
  }

  // Message handling
  function addMessage(content, type = 'assistant') {
    const messagesContainer = document.getElementById('aniter-widget-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `aniter-widget-message ${type}`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'aniter-widget-message-content';

    // Parse markdown for assistant messages
    if (type === 'assistant') {
      contentDiv.innerHTML = parseMarkdown(content);
    } else {
      contentDiv.innerHTML = content;
    }

    messageDiv.appendChild(contentDiv);
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  function showLoading() {
    const messagesContainer = document.getElementById('aniter-widget-messages');
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'aniter-widget-message assistant';
    loadingDiv.id = 'aniter-widget-loading';
    loadingDiv.innerHTML = `
      <div class="aniter-widget-message-content">
        <div class="aniter-widget-loading">
          <div class="aniter-widget-spinner"></div>
          Getting information for you...
        </div>
      </div>
    `;
    messagesContainer.appendChild(loadingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  function hideLoading() {
    const loadingDiv = document.getElementById('aniter-widget-loading');
    if (loadingDiv) {
      loadingDiv.remove();
    }
  }

  function showError(message) {
    const messagesContainer = document.getElementById('aniter-widget-messages');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'aniter-widget-error';
    errorDiv.textContent = message;
    messagesContainer.appendChild(errorDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  // API communication
  async function sendToAPI(message) {
    const requestBody = { query: message };
    if (sessionId) {
      requestBody.sessionId = sessionId;
    }

    const response = await fetch(`${getApiUrl()}/ask`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let assistantMessage = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data.trim()) {
            try {
              const parsedData = JSON.parse(data);

              if (parsedData.sessionId && parsedData.sessionId !== sessionId) {
                sessionId = parsedData.sessionId;
              }

              if (parsedData.type === 'content' && parsedData.content) {
                assistantMessage += parsedData.content;
                // Update the loading message with streaming content
                const loadingDiv = document.getElementById('aniter-widget-loading');
                if (loadingDiv) {
                  loadingDiv.querySelector('.aniter-widget-message-content').innerHTML = parseMarkdown(assistantMessage);
                }
              }

              if (parsedData.type === 'done') {
                return assistantMessage;
              }
            } catch (error) {
              console.error('Error parsing message:', error);
            }
          }
        }
      }
    }

    return assistantMessage;
  }

  // Public API
  window.AniterWidget = {
    init: function (options = {}) {
      if (isInitialized) return;

      // Merge config
      Object.assign(config, options);

      // Validate required options
      if (!config.apiKey) {
        console.error('AniterWidget: apiKey is required');
        return;
      }

      // Inject styles
      injectStyles();

      // Create and append widget
      const widget = createWidgetHTML();
      document.body.appendChild(widget);

      isInitialized = true;

      // Add welcome message
      setTimeout(() => {
        addMessage('Hello! I\'m your AI assistant. How can I help you today?', 'assistant');
      }, 500);
    },

    toggle: function () {
      if (!isInitialized) return;

      const chat = document.querySelector('.aniter-widget-chat');
      const input = document.getElementById('aniter-widget-input');

      isOpen = !isOpen;

      if (isOpen) {
        chat.classList.add('open');
        input.focus();
      } else {
        chat.classList.remove('open');
      }
    },

    open: function () {
      if (!isOpen) this.toggle();
    },

    close: function () {
      if (isOpen) this.toggle();
    },

    sendMessage: async function () {
      if (!isInitialized) return;

      const input = document.getElementById('aniter-widget-input');
      const sendButton = document.getElementById('aniter-widget-send');
      const message = input.value.trim();

      if (!message) return;

      // Add user message
      addMessage(message, 'user');
      input.value = '';
      sendButton.disabled = true;

      // Show loading
      showLoading();

      try {
        const response = await sendToAPI(message);
        hideLoading();
        addMessage(response, 'assistant');
      } catch (error) {
        hideLoading();
        showError('Sorry, I encountered an error. Please try again.');
        console.error('Widget API error:', error);
      } finally {
        sendButton.disabled = false;
        input.focus();
      }
    }
  };

})(); 