import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    hmr: {
      overlay: false,
    },
    proxy: {
      "/ask": {
        target: "http://localhost:3005",
        changeOrigin: true,
        secure: false,
        configure: (proxy) => {
          proxy.on("proxyReq", (_, req) => {
            console.log("🚀 Proxying request to backend:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req) => {
            console.log("📨 Backend response:", proxyRes.statusCode, req.url);
          });
          proxy.on("error", (err, req) => {
            console.error("❌ Proxy error:", err, req.url);
          });
        },
      },
      "/health": {
        target: "http://localhost:3005",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
