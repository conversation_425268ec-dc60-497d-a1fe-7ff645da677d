{"name": "ai-assistant", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"vite\"", "server": "tsx watch server/index.ts", "client": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@playwright/test": "^1.54.1", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^8.2.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tsx": "^4.7.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}