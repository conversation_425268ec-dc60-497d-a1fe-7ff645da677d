const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3001;

// Enable CORS for widget integration
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Serve static files
app.use(express.static(path.join(__dirname)));

// Widget endpoint
app.get('/widget.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.sendFile(path.join(__dirname, 'widget.js'));
});

// Demo page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'demo.html'));
});

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'ok', service: 'aniter-widget' });
});

app.listen(port, () => {
    console.log(`🚀 Widget server running on port ${port}`);
    console.log(`📱 Widget URL: http://localhost:${port}/widget.js`);
    console.log(`🎯 Demo page: http://localhost:${port}/`);
}); 