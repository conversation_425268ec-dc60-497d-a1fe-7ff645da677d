#!/bin/bash

echo "🚀 Railway.com Deployment Quick Start"
echo "======================================"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    npm install -g @railway/cli
fi

echo ""
echo "📋 Pre-deployment checklist:"
echo "✅ Project pushed to GitHub"
echo "✅ ANTIER_API_KEY ready"
echo "✅ Railway account created"
echo ""

# Prompt for API key
read -p "🔑 Enter your ANTIER_API_KEY: " api_key

echo ""
echo "🚂 Starting Railway deployment..."

# Login to Railway (will open browser)
echo "1. Logging into Railway..."
railway login

echo ""
echo "2. Creating new Railway project..."
railway init

echo ""
echo "🏗️  Deployment structure:"
echo "   Frontend: React + Vite (with <PERSON><PERSON>dy)"
echo "   Backend:  Express.js + TypeScript"
echo ""

echo "📖 Next steps:"
echo "1. Go to your Railway dashboard"
echo "2. Create a second service for the backend:"
echo "   - Add new service from same GitHub repo"
echo "   - Set root directory to 'server'"
echo "   - Add environment variable: ANTIER_API_KEY=$api_key"
echo "3. Generate domains for both services"
echo "4. Update frontend VITE_API_URL with backend URL"
echo ""
echo "📚 See RAILWAY_DEPLOYMENT.md for detailed instructions"
echo ""
echo "🎉 Happy deploying!" 