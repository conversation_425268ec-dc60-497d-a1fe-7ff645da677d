/* Modern Dark Theme CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #0a0a0a;
  color: #ffffff;
  min-height: 100vh;
  line-height: 1.6;
}

#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  background: #0a0a0a;
}

.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #111111;
  border-radius: 0;
  position: relative;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
  backdrop-filter: blur(10px);
}

.chat-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.chat-header p {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0;
  margin-top: 0.25rem;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: #0f0f0f;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #525252;
}

.message {
  display: flex;
  max-width: 85%;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  word-break: break-word;
  position: relative;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  align-self: flex-end;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.message.assistant {
  align-self: flex-start;
  background: #1f1f1f;
  color: #e5e7eb;
  border: 1px solid #2a2a2a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.message .timestamp {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  opacity: 0.7;
  font-weight: 400;
}

.message-content {
  width: 100%;
}

.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
  gap: 0.5rem;
}

.copy-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 0.375rem;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.9);
  min-width: 32px;
  height: 32px;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  transform: scale(1);
  border-color: rgba(255, 255, 255, 0.3);
}

.copy-button.copied {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.4);
  color: #22c55e;
  transform: scale(1);
}

.copy-button.copied:hover {
  background: rgba(34, 197, 94, 0.25);
}

.message:hover .copy-button {
  opacity: 1;
  transform: scale(1);
}

.message .copy-button {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Enhanced Markdown formatting styles */
.markdown-content {
  white-space: normal;
  overflow-wrap: break-word;
  word-break: normal;
  line-height: 1.6;
}

/* Heading Styles */
.markdown-h1,
.markdown-h2,
.markdown-h3,
.markdown-h4,
.markdown-h5,
.markdown-h6 {
  color: #ffffff;
  font-weight: 700;
  line-height: 1.3;
  margin: 1.5rem 0 0.75rem 0;
  position: relative;
}

.markdown-h1 {
  font-size: 1.75rem;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.markdown-h2 {
  font-size: 1.5rem;
  color: #3b82f6;
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  background: rgba(59, 130, 246, 0.05);
  padding: 0.75rem 0 0.75rem 1rem;
  border-radius: 0 8px 8px 0;
  margin: 1.5rem 0 1rem 0;
}

.markdown-h3 {
  font-size: 1.25rem;
  color: #60a5fa;
  position: relative;
  padding-left: 0.75rem;
}

.markdown-h3::before {
  content: "▶";
  position: absolute;
  left: 0;
  color: #60a5fa;
  font-size: 0.875rem;
}

.markdown-h4 {
  font-size: 1.125rem;
  color: #93c5fd;
  font-weight: 600;
}

.markdown-h5 {
  font-size: 1rem;
  color: #bfdbfe;
  font-weight: 600;
}

.markdown-h6 {
  font-size: 0.875rem;
  color: #dbeafe;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Text Formatting */
.markdown-strong {
  color: #ffffff;
  font-weight: 700;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.markdown-em {
  color: #d1d5db;
  font-style: italic;
  font-weight: 500;
}

.markdown-p {
  margin: 0.75rem 0;
  color: #e5e7eb;
  line-height: 1.7;
}

/* Code Formatting */
.markdown-code {
  background: linear-gradient(135deg, #2a2a2a, #1f1f1f);
  color: #fbbf24;
  padding: 0.2rem 0.4rem;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  border: 1px solid #404040;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.markdown-pre {
  background: linear-gradient(135deg, #1a1a1a, #0f0f0f);
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  padding: 1.25rem;
  margin: 1rem 0;
  overflow-x: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
}

.markdown-pre::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: 12px 12px 0 0;
}

.markdown-pre .markdown-code {
  background: none;
  border: none;
  padding: 0;
  color: #e5e7eb;
  font-size: 0.875rem;
  line-height: 1.6;
  box-shadow: none;
}

/* List Formatting */
.markdown-ul,
.markdown-ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
  color: #e5e7eb;
}

.markdown-li {
  margin: 0.5rem 0;
  padding-left: 0.5rem;
  line-height: 1.6;
}

.markdown-ul .markdown-li {
  position: relative;
}

.markdown-ul .markdown-li::before {
  content: "•";
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: -0.75rem;
}

/* Link Formatting */
.markdown-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 1px solid transparent;
}

.markdown-link:hover {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
}

/* Blockquote Formatting */
.markdown-blockquote {
  border-left: 4px solid #3b82f6;
  padding: 1rem 0 1rem 1.5rem;
  margin: 1rem 0;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 0 8px 8px 0;
  color: #d1d5db;
  font-style: italic;
  position: relative;
}

.markdown-blockquote::before {
  content: '"';
  font-size: 3rem;
  color: #3b82f6;
  position: absolute;
  left: 0.5rem;
  top: -0.5rem;
  opacity: 0.3;
}

/* Horizontal Rule */
.markdown-hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  margin: 2rem 0;
  border-radius: 1px;
}

.message.assistant .email-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.message.assistant .email-link:hover {
  color: #60a5fa;
  text-decoration: underline;
}

.chat-input {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  padding: 1.5rem 2rem;
  background: #0f0f0f;
  /* border-top: 1px solid #2a2a2a; */
  position: relative;
}

.input-field {
  flex: 1;
  background: #0f0f0f;
  border: 2px solid #404040;
  border-radius: 12px;
  padding: 1rem 1.25rem;
  color: #ffffff;
  font-size: 1rem;
  outline: none;
  transition: all 0.2s ease;
  resize: none;
  min-height: 48px;
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.5;
}

.input-field::-webkit-scrollbar {
  width: 6px;
}

.input-field::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 6px;
}

.input-field::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 6px;
}

.input-field::-webkit-scrollbar-thumb:hover {
  background: #525252;
}

.input-field:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field::placeholder {
  color: #9ca3af;
}

.input-field:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-width: 80px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

.send-button:disabled {
  background: #404040;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  color: #ffffff;
  padding: 2rem;
}

.welcome-screen h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-screen p {
  color: #9ca3af;
  font-size: 1.125rem;
  max-width: 500px;
  line-height: 1.6;
}

.ai-avatar {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-weight: 700;
  font-size: 1.25rem;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  margin: 0.5rem 0;
  animation: errorSlideIn 0.3s ease-out;
  max-width: 100%;
  align-self: stretch;
}

/* Error type variants */
.error-network {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.error-server {
  background: rgba(245, 101, 101, 0.1);
  border-color: rgba(245, 101, 101, 0.2);
}

.error-api {
  background: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.2);
}

.error-rate_limit {
  background: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.2);
}

.error-timeout {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.error-unknown {
  background: rgba(156, 163, 175, 0.1);
  border-color: rgba(156, 163, 175, 0.2);
}

.error-content {
  padding: 1rem 1.25rem;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.error-icon {
  font-size: 1.25rem;
}

.error-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: #ffffff;
  flex: 1;
}

.error-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.error-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.error-description {
  color: #d1d5db;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 0.75rem 0;
}

.error-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.error-time {
  font-size: 0.75rem;
  color: #9ca3af;
  opacity: 0.8;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.retry-button {
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #93c5fd;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
  color: #bfdbfe;
}

.retry-button:active {
  transform: translateY(0);
}

/* Error-specific retry button colors */
.error-network .retry-button {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

.error-server .retry-button {
  background: rgba(245, 101, 101, 0.2);
  border-color: rgba(245, 101, 101, 0.3);
  color: #fca5a5;
}

.error-api .retry-button {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.3);
  color: #fcd34d;
}

.error-rate_limit .retry-button {
  background: rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.3);
  color: #c4b5fd;
}

.error-timeout .retry-button {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.3);
  color: #fbbf24;
}

.error-unknown .retry-button {
  background: rgba(156, 163, 175, 0.2);
  border-color: rgba(156, 163, 175, 0.3);
  color: #d1d5db;
}

.stop-button {
  padding: 0.5rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: #fca5a5;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.stop-button:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

.clear-session-button {
  padding: 0.5rem 1rem;
  background: rgba(75, 85, 99, 0.1);
  border: 1px solid rgba(75, 85, 99, 0.2);
  border-radius: 8px;
  color: #9ca3af;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-session-button:hover {
  background: rgba(75, 85, 99, 0.2);
  border-color: rgba(107, 114, 128, 0.3);
  color: #d1d5db;
  transform: translateY(-1px);
}

.typing-indicator {
  display: inline-block;
  width: 8px;
  height: 16px;
  background: #3b82f6;
  border-radius: 2px;
  animation: typing 1.4s infinite ease-in-out;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    padding: 1rem 1.5rem;
  }
  
  .chat-messages {
    padding: 1.5rem;
  }
  
  .chat-input {
    padding: 1rem 1.5rem;
  }
  
  .message {
    max-width: 90%;
    padding: 0.875rem 1rem;
  }
  
  .welcome-screen h2 {
    font-size: 1.5rem;
  }
  
  .welcome-screen p {
    font-size: 1rem;
  }
  
  .markdown-h1 {
    font-size: 1.5rem;
  }
  
  .markdown-h2 {
    font-size: 1.25rem;
    padding: 0.5rem 0 0.5rem 0.75rem;
  }
  
  .markdown-h3 {
    font-size: 1.125rem;
  }
  
  .markdown-h4 {
    font-size: 1rem;
  }
  
  .markdown-h5 {
    font-size: 0.875rem;
  }
  
  .markdown-h6 {
    font-size: 0.75rem;
  }
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Enhanced Loading Message */
.loading-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 0;
  color: #9ca3af;
  font-size: 0.875rem;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Streaming text cursor animation - only for streaming content */
.message.assistant .markdown-content {
  position: relative;
}

.message.assistant.streaming .markdown-content::after {
  content: '';
  position: absolute;
  right: 0;
  width: 2px;
  height: 1.2em;
  background: #3b82f6;
  animation: blink 1.5s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Focus states for accessibility */
.send-button:focus,
.input-field:focus,
.retry-button:focus,
.stop-button:focus,
.clear-session-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
