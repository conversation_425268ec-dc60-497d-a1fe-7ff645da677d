import React, { useState, useRef, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import { StreamingService } from "../services/streamingService";
import type { StreamingMessage } from "../services/streamingService";

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
  connectionEstablished?: boolean;
  waitingForResponse?: boolean;
}

interface CopyButtonProps {
  content: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ content }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      // Remove HTML tags from content for clean text copying
      const textContent = content
        .replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, " ");

      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(textContent);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = textContent;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        textArea.remove();
      }

      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className={`copy-button ${copied ? "copied" : ""}`}
      title={copied ? "Copied!" : "Copy message"}
      aria-label="Copy message to clipboard"
    >
      {copied ? (
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <polyline points="20,6 9,17 4,12"></polyline>
        </svg>
      ) : (
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="m5,15 L5,5 A2,2 0 0,1 7,3 L17,3"></path>
        </svg>
      )}
    </button>
  );
};

// Add key phrase bolding function
const processKeyPhrases = (content: string): string => {
  const keyPhrases = [
    "GDPR",
    "IT Act",
    "mandatory breach reporting",
    "data protection",
    "cybersecurity",
    "ISO 27001",
    "SOC 2",
    "HIPAA",
    "PCI DSS",
    "data breach",
    "incident response",
    "compliance",
    "privacy policy",
    "security policy",
    "access control",
    "risk assessment",
    "audit",
    "vulnerability",
    "encryption",
    "backup",
    "disaster recovery",
    "business continuity",
  ];

  let processedContent = content;

  // First, clean up any existing HTML tags and normalize the content
  processedContent = processedContent
    .replace(/<br\s*\/?>/gi, "\n")
    .replace(/<\/?em>/gi, "")
    .replace(/<\/?strong>/gi, "")
    .replace(/&nbsp;/gi, " ")
    .replace(/<[^>]*>/g, "")
    .trim();

  // Process key phrases, but be careful not to bold them if they're in headers
  keyPhrases.forEach((phrase) => {
    // Only bold key phrases that are not part of markdown headers
    const regex = new RegExp(
      `^(?!#+\\s.*)(.*?\\b)(${phrase.replace(
        /[.*+?^${}()|[\]\\]/g,
        "\\$&"
      )})(\\b.*)$`,
      "gim"
    );
    processedContent = processedContent.replace(regex, "$1**$2**$3");
  });

  return processedContent;
};

const cleanAndFormatContent = (content: string): string => {
  // Clean up content and ensure proper markdown formatting
  let cleaned = content
    .replace(/<br\s*\/?>/gi, "\n")
    .replace(/&nbsp;/gi, " ")
    .replace(/<\/?em>/gi, "")
    .replace(/<\/?strong>/gi, "")
    .replace(/<[^>]*>/g, "")
    .trim();

  // Normalize line breaks and spacing
  cleaned = cleaned
    .replace(/\n\s*\n\s*\n/g, "\n\n")
    .replace(/---+/g, "\n---\n")
    .trim();

  return cleaned;
};

export const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [retryAttempt, setRetryAttempt] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [connectionTimeout, setConnectionTimeout] =
    useState<NodeJS.Timeout | null>(null);
  const [error, setError] = useState<{
    message: string;
    type: "network" | "server" | "api" | "rate_limit" | "timeout" | "unknown";
    canRetry: boolean;
    timestamp: Date;
  } | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatMessagesRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const streamingService = useRef(new StreamingService());

  // Track if user is at (or near) the bottom
  const [isAtBottom, setIsAtBottom] = useState(true);

  // Handler to check scroll position
  const handleScroll = () => {
    const chatDiv = chatMessagesRef.current;
    if (!chatDiv) return;
    const threshold = 80; // px from bottom to still count as "at bottom"
    const atBottom =
      chatDiv.scrollHeight - chatDiv.scrollTop - chatDiv.clientHeight <
      threshold;
    setIsAtBottom(atBottom);
  };

  // Only scroll to bottom if user is at (or near) the bottom
  const scrollToBottom = () => {
    if (isAtBottom) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(() => {
    scrollToBottom();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages]);

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const parseErrorMessage = (errorMsg: string, errorType?: string) => {
    const timestamp = new Date();

    // Network-related errors
    if (
      errorMsg.includes("Failed to fetch") ||
      errorMsg.includes("network") ||
      errorType === "network"
    ) {
      return {
        message:
          "Network connection error. Please check your internet connection and try again.",
        type: "network" as const,
        canRetry: true,
        timestamp,
      };
    }

    // Rate limiting
    if (
      errorMsg.includes("rate limit") ||
      errorMsg.includes("429") ||
      errorMsg.includes("Too Many Requests")
    ) {
      return {
        message:
          "Rate limit exceeded. Please wait a moment before trying again.",
        type: "rate_limit" as const,
        canRetry: true,
        timestamp,
      };
    }

    // Server errors (5xx)
    if (
      errorMsg.includes("500") ||
      errorMsg.includes("502") ||
      errorMsg.includes("503") ||
      errorMsg.includes("504")
    ) {
      return {
        message:
          "Server is temporarily unavailable. Please try again in a few moments.",
        type: "server" as const,
        canRetry: true,
        timestamp,
      };
    }

    // API key or authentication errors
    if (
      errorMsg.includes("401") ||
      errorMsg.includes("403") ||
      errorMsg.includes("Unauthorized") ||
      errorMsg.includes("Forbidden")
    ) {
      return {
        message:
          "Authentication error. Please contact support if this problem persists.",
        type: "api" as const,
        canRetry: false,
        timestamp,
      };
    }

    // Timeout errors
    if (errorMsg.includes("timeout") || errorMsg.includes("AbortError")) {
      return {
        message:
          "Request timed out. Please try again with a shorter message or check your connection.",
        type: "timeout" as const,
        canRetry: true,
        timestamp,
      };
    }

    // External API errors
    if (errorMsg.includes("External API error")) {
      return {
        message:
          "The AI service is temporarily unavailable. Please try again later.",
        type: "api" as const,
        canRetry: true,
        timestamp,
      };
    }

    // Default fallback
    return {
      message: errorMsg || "An unexpected error occurred. Please try again.",
      type: "unknown" as const,
      canRetry: true,
      timestamp,
    };
  };

  const getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case "network":
        return "🌐";
      case "server":
        return "🔧";
      case "api":
        return "🔑";
      case "rate_limit":
        return "⏱️";
      case "timeout":
        return "⏰";
      default:
        return "⚠️";
    }
  };

  const handleClearError = () => {
    setError(null);
  };

  const adjustTextareaHeight = () => {
    const textarea = inputRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      const scrollHeight = textarea.scrollHeight;

      if (scrollHeight <= 200) {
        // Still growing, set height to content
        textarea.style.height = `${Math.max(scrollHeight, 48)}px`;
        textarea.style.overflowY = "hidden";
      } else {
        // Max height reached, enable scrolling
        textarea.style.height = "200px";
        textarea.style.overflowY = "auto";
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    adjustTextareaHeight();
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: generateId(),
      type: "user",
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    const assistantMessageId = generateId();
    const assistantMessage: Message = {
      id: assistantMessageId,
      type: "assistant",
      content: "",
      timestamp: new Date(),
      isStreaming: true,
      connectionEstablished: false,
      waitingForResponse: true,
    };

    setMessages((prev) => [...prev, userMessage, assistantMessage]);
    setInputValue("");
    setIsLoading(true);
    setError(null);
    setRetryAttempt(0);

    // Set a timeout to update the message if OpenRouter takes too long to start responding
    const responseTimeout = setTimeout(() => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessageId
            ? {
                ...msg,
                connectionEstablished: true,
                waitingForResponse: true,
              }
            : msg
        )
      );
    }, 2000); // After 2 seconds, show "Getting information for you..."

    setConnectionTimeout(responseTimeout);

    try {
      // Set the current session ID in the streaming service
      streamingService.current.setSessionId(sessionId);

      await streamingService.current.sendMessage(userMessage.content, {
        onMessage: (data: StreamingMessage) => {
          // Mark connection as established when we receive any message
          setMessages((prev) =>
            prev.map((msg) =>
              msg.id === assistantMessageId
                ? { ...msg, connectionEstablished: true }
                : msg
            )
          );

          if (data.type === "content" && data.content) {
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? {
                      ...msg,
                      content: msg.content + data.content,
                      waitingForResponse: false,
                    }
                  : msg
              )
            );
          } else if (data.type === "done") {
            setIsLoading(false);
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? {
                      ...msg,
                      isStreaming: false,
                      waitingForResponse: false,
                    }
                  : msg
              )
            );
          } else if (data.type === "error") {
            const errorDetails = parseErrorMessage(
              data.error || "An error occurred",
              data.errorType
            );
            setError(errorDetails);
            setIsLoading(false);
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? {
                      ...msg,
                      isStreaming: false,
                      waitingForResponse: false,
                    }
                  : msg
              )
            );
          }
        },
        onSessionUpdate: (newSessionId: string) => {
          console.log("🔗 Session updated in ChatInterface:", newSessionId);
          setSessionId(newSessionId);
        },
        onError: (errorMsg: string, errorType?: string) => {
          const errorDetails = parseErrorMessage(errorMsg, errorType);
          setError(errorDetails);
          setIsLoading(false);
          setMessages((prev) =>
            prev.map((msg) =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    isStreaming: false,
                    waitingForResponse: false,
                  }
                : msg
            )
          );
        },
        onComplete: () => {
          setIsLoading(false);
        },
        onRetry: (attempt: number) => {
          setRetryAttempt(attempt);
        },
      });
    } catch (error) {
      console.error("Connection error:", error);
      const errorDetails = parseErrorMessage(
        "Failed to establish connection to the server.",
        "network"
      );
      setError(errorDetails);
      setIsLoading(false);
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessageId
            ? {
                ...msg,
                isStreaming: false,
                waitingForResponse: false,
              }
            : msg
        )
      );
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleStopGeneration = () => {
    streamingService.current.abort();
    setIsLoading(false);
    setMessages((prev) =>
      prev.map((msg) =>
        msg.isStreaming
          ? {
              ...msg,
              isStreaming: false,
              waitingForResponse: false,
            }
          : msg
      )
    );
  };

  const handleRetry = () => {
    if (messages.length >= 2) {
      const lastUserMessage = messages[messages.length - 2];
      if (lastUserMessage.type === "user") {
        setMessages((prev) => prev.slice(0, -1));
        setInputValue(lastUserMessage.content);
        setTimeout(() => handleSendMessage(), 100);
      }
    }
  };

  const handleClearSession = () => {
    setSessionId(null);
    setMessages([]);
    streamingService.current.clearSession();
    console.log("🧹 Session cleared");
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      streamingService.current.abort();
    };
  }, []);

  // Remove cyclic loading message here

  return (
    <div className="chat-interface">
      {/* Header */}
      <div className="chat-header">
        <div style={{ display: "flex", alignItems: "center", gap: "0.75rem" }}>
          <img
            src="/logo.jpeg"
            alt="AI Assistant Logo"
            className="ai-logo"
            style={{
              width: "40px",
              height: "40px",
              borderRadius: "8px",
              objectFit: "cover",
            }}
          />
          <div>
            <h1>Policy Assistant</h1>
            <p>
              {isLoading
                ? retryAttempt > 0
                  ? `Retrying... (${retryAttempt}/3)`
                  : "Thinking..."
                : sessionId
                ? `Connected (Session: ${sessionId.slice(0, 8)}...)`
                : "Ready to help"}
            </p>
          </div>
        </div>
        <div style={{ display: "flex", gap: "0.5rem", alignItems: "center" }}>
          {sessionId && messages.length > 0 && (
            <button
              onClick={handleClearSession}
              className="clear-session-button"
              title="Start new conversation"
            >
              New Chat
            </button>
          )}
          {isLoading && (
            <button onClick={handleStopGeneration} className="stop-button">
              Stop
            </button>
          )}
        </div>
      </div>

      {/* Remove cyclic loading message here */}

      {/* Messages */}
      <div
        className="chat-messages"
        ref={chatMessagesRef}
        onScroll={handleScroll}
        style={{ overflowY: "auto", height: "100%" }}
      >
        {messages.length === 0 && (
          <div className="welcome-screen">
            <img
              src="/logo.jpeg"
              alt="AI Assistant Logo"
              className="ai-logo-large"
              style={{
                width: "80px",
                height: "80px",
                borderRadius: "16px",
                objectFit: "cover",
                marginBottom: "1rem",
              }}
            />
            <h2>Welcome to Antier Policy Assistant</h2>
            <p>
              Ask me anything about Antier's policies, procedures, and
              guidelines. I'm here to help you navigate our organizational
              policies with accurate, real-time responses!
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className={`message ${message.type}`}>
            <div className="message-content">
              {message.type === "assistant" ? (
                <div className="markdown-content">
                  <ReactMarkdown
                    components={{
                      h1: ({ children }) => (
                        <h1 className="markdown-h1">{children}</h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="markdown-h2">{children}</h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="markdown-h3">{children}</h3>
                      ),
                      h4: ({ children }) => (
                        <h4 className="markdown-h4">{children}</h4>
                      ),
                      h5: ({ children }) => (
                        <h5 className="markdown-h5">{children}</h5>
                      ),
                      h6: ({ children }) => (
                        <h6 className="markdown-h6">{children}</h6>
                      ),
                      strong: ({ children }) => (
                        <strong className="markdown-strong">{children}</strong>
                      ),
                      em: ({ children }) => (
                        <em className="markdown-em">{children}</em>
                      ),
                      code: ({ children }) => (
                        <code className="markdown-code">{children}</code>
                      ),
                      pre: ({ children }) => (
                        <pre className="markdown-pre">{children}</pre>
                      ),
                      ul: ({ children }) => (
                        <ul className="markdown-ul">{children}</ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="markdown-ol">{children}</ol>
                      ),
                      li: ({ children }) => (
                        <li className="markdown-li">{children}</li>
                      ),
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          className="markdown-link"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {children}
                        </a>
                      ),
                      p: ({ children }) => (
                        <p className="markdown-p">{children}</p>
                      ),
                      blockquote: ({ children }) => (
                        <blockquote className="markdown-blockquote">
                          {children}
                        </blockquote>
                      ),
                      hr: () => <hr className="markdown-hr" />,
                    }}
                  >
                    {processKeyPhrases(cleanAndFormatContent(message.content))}
                  </ReactMarkdown>
                </div>
              ) : (
                <div
                  style={{
                    whiteSpace: "pre-wrap",
                    overflowWrap: "break-word",
                    wordBreak: "normal",
                    lineHeight: "1.5",
                  }}
                >
                  {message.content}
                </div>
              )}
              {message.isStreaming && !message.content && (
                <div className="loading-message">
                  <div className="loading-spinner"></div>
                  <span>
                    {!message.connectionEstablished
                      ? "Connecting..."
                      : message.waitingForResponse
                      ? "Getting information for you..."
                      : "Thinking..."}
                  </span>
                </div>
              )}
              <div className="message-footer">
                <div className="timestamp">
                  {message.timestamp.toLocaleTimeString()}
                </div>
                {!message.isStreaming && message.content && (
                  <CopyButton content={message.content} />
                )}
              </div>
            </div>
          </div>
        ))}

        {error && (
          <div className={`error-message error-${error.type}`}>
            <div className="error-content">
              <div className="error-header">
                <span className="error-icon">{getErrorIcon(error.type)}</span>
                <span className="error-title">
                  {error.type === "network" && "Connection Error"}
                  {error.type === "server" && "Server Error"}
                  {error.type === "api" && "Service Error"}
                  {error.type === "rate_limit" && "Rate Limited"}
                  {error.type === "timeout" && "Request Timeout"}
                  {error.type === "unknown" && "Unexpected Error"}
                </span>
                <button
                  onClick={handleClearError}
                  className="error-close"
                  title="Dismiss"
                >
                  ✕
                </button>
              </div>
              <p className="error-description">{error.message}</p>
              <div className="error-actions">
                {error.canRetry && (
                  <button onClick={handleRetry} className="retry-button">
                    Try Again
                  </button>
                )}
                <span className="error-time">
                  {error.timestamp.toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="chat-input">
        <textarea
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          placeholder="Type your message..."
          disabled={isLoading}
          className={`input-field ${isLoading ? "loading" : ""}`}
          rows={1}
        />
        <button
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || isLoading}
          className="send-button"
        >
          {isLoading ? "Sending..." : "Send"}
        </button>
      </div>
    </div>
  );
};
