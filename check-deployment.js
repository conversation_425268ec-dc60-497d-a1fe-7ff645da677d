#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🚀 Railway Deployment Check\n');

let allChecksPassed = true;

// Check 1: Required files exist
console.log('📁 Checking required files...');
const requiredFiles = [
    'package.json',
    'nixpacks.toml',
    'Caddyfile',
    'server/index.ts',
    'server/package.json',
    'widget/widget.js',
    'widget/demo.html',
    'src/services/streamingService.ts'
];

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        allChecksPassed = false;
    }
});

// Check 2: Package.json scripts
console.log('\n📦 Checking package.json scripts...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredScripts = ['dev', 'build', 'start', 'lint'];

    requiredScripts.forEach(script => {
        if (packageJson.scripts && packageJson.scripts[script]) {
            console.log(`✅ ${script} script exists`);
        } else {
            console.log(`❌ ${script} script missing`);
            allChecksPassed = false;
        }
    });
} catch (error) {
    console.log('❌ Error reading package.json');
    allChecksPassed = false;
}

// Check 3: Server package.json
console.log('\n🔧 Checking server package.json...');
try {
    const serverPackageJson = JSON.parse(fs.readFileSync('server/package.json', 'utf8'));
    if (serverPackageJson.dependencies && serverPackageJson.dependencies.express) {
        console.log('✅ Express dependency found');
    } else {
        console.log('❌ Express dependency missing');
        allChecksPassed = false;
    }
} catch (error) {
    console.log('❌ Error reading server/package.json');
    allChecksPassed = false;
}

// Check 4: Widget configuration
console.log('\n🎯 Checking widget configuration...');
try {
    const widgetJs = fs.readFileSync('widget/widget.js', 'utf8');
    if (widgetJs.includes('getApiUrl()')) {
        console.log('✅ Widget has auto-detection for API URL');
    } else {
        console.log('❌ Widget missing API URL auto-detection');
        allChecksPassed = false;
    }

    if (widgetJs.includes('/ask')) {
        console.log('✅ Widget configured to use /ask endpoint');
    } else {
        console.log('❌ Widget not configured for /ask endpoint');
        allChecksPassed = false;
    }
} catch (error) {
    console.log('❌ Error reading widget.js');
    allChecksPassed = false;
}

// Check 5: Backend server configuration
console.log('\n⚙️ Checking backend server configuration...');
try {
    const serverTs = fs.readFileSync('server/index.ts', 'utf8');
    if (serverTs.includes('/widget')) {
        console.log('✅ Backend serves widget files');
    } else {
        console.log('❌ Backend missing widget file serving');
        allChecksPassed = false;
    }

    if (serverTs.includes('/ask')) {
        console.log('✅ Backend has /ask endpoint');
    } else {
        console.log('❌ Backend missing /ask endpoint');
        allChecksPassed = false;
    }

      if (serverTs.includes('cors')) {
    console.log('✅ Backend has CORS configuration');
  } else {
    console.log('❌ Backend missing CORS configuration');
    allChecksPassed = false;
  }
} catch (error) {
    console.log('❌ Error reading server/index.ts');
    allChecksPassed = false;
}

// Check 6: Frontend service configuration
console.log('\n🌐 Checking frontend service configuration...');
try {
    const streamingService = fs.readFileSync('src/services/streamingService.ts', 'utf8');
    if (streamingService.includes('VITE_API_URL')) {
        console.log('✅ Frontend uses environment variable for API URL');
    } else {
        console.log('❌ Frontend not configured for environment variable');
        allChecksPassed = false;
    }
} catch (error) {
    console.log('❌ Error reading streamingService.ts');
    allChecksPassed = false;
}

// Check 7: Railway configuration
console.log('\n🚂 Checking Railway configuration...');
try {
    const nixpacks = fs.readFileSync('nixpacks.toml', 'utf8');
    if (nixpacks.includes('caddy')) {
        console.log('✅ Nixpacks configured with Caddy');
    } else {
        console.log('❌ Nixpacks missing Caddy configuration');
        allChecksPassed = false;
    }

    if (nixpacks.includes('build')) {
        console.log('✅ Nixpacks has build phase');
    } else {
        console.log('❌ Nixpacks missing build phase');
        allChecksPassed = false;
    }
} catch (error) {
    console.log('❌ Error reading nixpacks.toml');
    allChecksPassed = false;
}

// Check 8: Caddyfile configuration
console.log('\n📄 Checking Caddyfile configuration...');
try {
    const caddyfile = fs.readFileSync('Caddyfile', 'utf8');
    if (caddyfile.includes('dist')) {
        console.log('✅ Caddyfile serves from dist directory');
    } else {
        console.log('❌ Caddyfile not configured for dist directory');
        allChecksPassed = false;
    }

    if (caddyfile.includes('index.html')) {
        console.log('✅ Caddyfile configured for SPA routing');
    } else {
        console.log('❌ Caddyfile missing SPA routing configuration');
        allChecksPassed = false;
    }
} catch (error) {
    console.log('❌ Error reading Caddyfile');
    allChecksPassed = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allChecksPassed) {
    console.log('🎉 All checks passed! Ready for Railway deployment.');
    console.log('\n📋 Next steps:');
    console.log('1. git add .');
    console.log('2. git commit -m "Ready for Railway deployment"');
    console.log('3. git push');
    console.log('4. Create Railway project from GitHub repo');
    console.log('5. Deploy backend service (root: server)');
    console.log('6. Deploy frontend service (root: .)');
    console.log('7. Set environment variables');
    console.log('8. Configure networking');
} else {
    console.log('❌ Some checks failed. Please fix the issues above before deploying.');
    process.exit(1);
} 