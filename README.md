# AI Assistant

A React-based AI assistant application with secure backend API handling.

## Features

- Real-time streaming chat interface
- Secure API key handling on backend
- Modern React UI with TypeScript
- Express.js backend for API security

## Security

The application now uses a secure backend architecture where:

- API keys are stored securely on the server side
- Frontend never exposes sensitive API credentials
- All external API calls are proxied through the backend

## Setup

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Environment Configuration (Optional):**

   - Copy `.env.example` to `.env` if you want to use custom API keys
   - Set your `ANTIER_API_KEY` in the `.env` file
   - Default API key is used if not specified

3. **Development:**

   ```bash
   # Runs both frontend and backend concurrently
   npm run dev

   # Or run them separately:
   npm run server  # Backend only (port 3001)
   npm run client  # Frontend only (port 5173)
   ```

## Architecture

### Backend (Express.js)

- **Port:** 3005 (default)
- **Main endpoint:** `POST /ask` - Handles chat queries securely
- **Health check:** `GET /health` - Server status

### Frontend (React + Vite)

- **Port:** 5173 (default)
- **Proxy configuration:** Routes `/ask` and `/health` to backend
- **No direct external API calls**

## API Endpoints

### `POST /ask`

Handles chat queries and returns streaming responses.

**Request:**

```json
{
  "query": "Your question here"
}
```

**Response:**
Server-Sent Events (SSE) stream with chat responses.

### `GET /health`

Returns server health status.

## Development Commands

- `npm run dev` - Start both frontend and backend
- `npm run server` - Start backend server only
- `npm run client` - Start frontend only
- `npm run build` - Build for production
- `npm run lint` - Run ESLint

## Security Best Practices

1. **API Key Security:** Never expose API keys in frontend code
2. **Environment Variables:** Use `.env` files for sensitive configuration
3. **CORS:** Properly configured for development and production
4. **Error Handling:** Secure error messages without exposing internals
