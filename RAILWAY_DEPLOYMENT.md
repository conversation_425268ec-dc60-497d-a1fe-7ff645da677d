# Railway.com Deployment Guide

This guide walks you through deploying your full stack AI Assistant application to Railway.com.

## Project Structure

Your application is now structured for Railway deployment:

```
ai-assistant/
├── src/                    # React frontend source
├── server/                 # Express.js backend
│   ├── package.json       # Backend dependencies
│   ├── tsconfig.json      # Backend TypeScript config
│   └── index.ts           # Backend server code
├── nixpacks.toml          # Frontend build configuration
├── Caddyfile              # Static file server configuration
├── package.json           # Frontend dependencies
└── vite.config.ts         # Frontend build config
```

## Prerequisites

1. **Railway Account**: Sign up at [Railway.com](https://railway.app)
2. **GitHub Account**: Your code must be in a GitHub repository
3. **Environment Variables**: Have your `ANTIER_API_KEY` ready

## Deployment Steps

### Step 1: Push to GitHub

First, push your code to GitHub:

```bash
# Initialize git if not already done
git init

# Add and commit your changes
git add .
git commit -m "Prepare for Railway deployment"

# Add your GitHub repository as origin
git remote add origin YOUR_GITHUB_REPO_URL
git push -u origin main
```

### Step 2: Create Railway Project

1. Log in to [Railway.com](https://railway.app)
2. Click **"New Project"**
3. Select **"Deploy from GitHub repo"**
4. Choose your repository
5. Railway will create a project for you

### Step 3: Deploy Backend Service

1. In your Railway project, you'll see a service was created
2. Click on the service
3. Go to **Settings** → **Source**
4. Set **Root Directory** to: `server`
5. The service should automatically detect it's a Node.js app and deploy

### Step 4: Set Backend Environment Variables

1. In the backend service, go to **Variables** tab
2. Add the following variables:
   ```
   ANTIER_API_KEY=your_api_key_here
   NODE_ENV=production
   ```

### Step 5: Deploy Frontend Service

1. In your Railway project, click **"New Service"**
2. Select **"GitHub Repo"** and choose the same repository
3. Set **Root Directory** to: `.` (root of the project)
4. Railway will detect the nixpacks.toml and deploy the frontend

### Step 6: Configure Networking

#### Backend Service:

1. Go to **Settings** → **Networking**
2. Click **"Generate Domain"** to get a public URL
3. Copy the backend URL (e.g., `https://your-backend.railway.app`)

#### Frontend Service:

1. Go to **Settings** → **Networking**
2. Click **"Generate Domain"** to get a public URL
3. Update the frontend to use the backend URL

### Step 7: Update Frontend Configuration

You need to update the frontend to call the backend URL in production:

1. In your frontend service, go to **Variables**
2. Add:

   ```
   VITE_API_URL=https://your-backend.railway.app
   ```

3. Update `src/services/streamingService.ts`:
   ```typescript
   // Replace the URL line in attemptConnection method:
   const url = import.meta.env.VITE_API_URL
     ? `${import.meta.env.VITE_API_URL}/ask`
     : `/ask`;
   ```

### Step 8: Redeploy Frontend

After adding the environment variable:

1. Go to **Deployments** tab
2. Click **"Redeploy"** on the latest deployment

## Verification

1. Visit your frontend URL
2. Try sending a message
3. Check both service logs for any errors

## Environment Variables Reference

### Backend Service

- `ANTIER_API_KEY`: Your API key for the external service
- `NODE_ENV`: Set to `production`
- `PORT`: Automatically set by Railway

### Frontend Service

- `VITE_API_URL`: Your backend service URL

## Troubleshooting

### Common Issues:

1. **Backend not starting**: Check the logs for TypeScript compilation errors
2. **Frontend 404s**: Ensure Caddyfile is properly configured for SPA routing
3. **CORS errors**: Backend already has CORS configured for all origins
4. **API calls failing**: Verify the `VITE_API_URL` is set correctly

### Checking Logs:

- Click on each service → **Deployments** → **View Logs**

## Costs

Railway offers:

- **Hobby Plan**: $5/month per service
- **Free Trial**: 500 hours of compute time
- See [Railway Pricing](https://railway.app/pricing) for details

## Custom Domain (Optional)

To use your own domain:

1. Go to service **Settings** → **Networking**
2. Click **"Custom Domain"**
3. Add your domain and follow DNS setup instructions

## Support

- [Railway Documentation](https://docs.railway.com/)
- [Railway Discord](https://discord.gg/railway)
- [Railway Help Station](https://station.railway.com/)
