<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aniter Widget Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .feature h3 {
            margin-top: 0;
            color: #ffd700;
        }

        .integration-code {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
        }

        .integration-code pre {
            margin: 0;
            color: #00ff00;
            font-family: 'Courier New', monospace;
        }

        .btn {
            background: #ffd700;
            color: #333;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: scale(1.05);
        }

        .test-section {
            text-align: center;
            margin: 40px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 Aniter AI Widget Demo</h1>

        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Experience the power of AI assistance on any website with our easy-to-integrate widget!
        </p>

        <div class="features">
            <div class="feature">
                <h3>💬 Smart Conversations</h3>
                <p>Powered by advanced AI that understands context and provides helpful responses</p>
            </div>
            <div class="feature">
                <h3>🎨 Beautiful Design</h3>
                <p>Modern, responsive interface that looks great on any website</p>
            </div>
            <div class="feature">
                <h3>⚡ Easy Integration</h3>
                <p>Just add a few lines of code to your website</p>
            </div>
            <div class="feature">
                <h3>📱 Mobile Friendly</h3>
                <p>Works perfectly on desktop, tablet, and mobile devices</p>
            </div>
        </div>

        <div class="test-section">
            <h2>Test the Widget</h2>
            <p>Click the chat button in the bottom-right corner to try it out!</p>
            <button class="btn" onclick="AniterWidget.open()">Open Chat</button>
            <button class="btn" onclick="AniterWidget.close()">Close Chat</button>
        </div>

        <h2>Integration Code</h2>
        <p>Add this code to your website to integrate the Aniter AI Widget:</p>

        <div class="integration-code">
            <pre><code>&lt;!-- Add this script tag to your HTML head or body --&gt;
&lt;script src="https://your-domain.com/widget.js"&gt;&lt;/script&gt;

&lt;!-- Or if served from the same backend --&gt;
&lt;script src="/widget.js"&gt;&lt;/script&gt;

&lt;!-- Initialize the widget --&gt;
&lt;script&gt;
  AniterWidget.init({
    apiKey: 'your-api-key-here',
    theme: 'light', // 'light' or 'dark'
    position: 'bottom-right', // 'bottom-right' or 'bottom-left'
    title: 'AI Assistant'
  });
&lt;/script&gt;</code></pre>
        </div>

        <h2>Configuration Options</h2>
        <div class="features">
            <div class="feature">
                <h3>🎨 Theme</h3>
                <p><code>theme: 'light'</code> or <code>theme: 'dark'</code></p>
            </div>
            <div class="feature">
                <h3>📍 Position</h3>
                <p><code>position: 'bottom-right'</code> or <code>position: 'bottom-left'</code></p>
            </div>
            <div class="feature">
                <h3>📝 Title</h3>
                <p><code>title: 'Your Custom Title'</code></p>
            </div>
            <div class="feature">
                <h3>🔑 API Key</h3>
                <p><code>apiKey: 'your-api-key'</code> (required)</p>
            </div>
        </div>

        <div class="test-section">
            <h2>Try Different Themes</h2>
            <button class="btn" onclick="switchTheme('light')">Light Theme</button>
            <button class="btn" onclick="switchTheme('dark')">Dark Theme</button>
        </div>
    </div>

    <!-- Widget Script -->
    <script src="widget.js"></script>
    <script>
        // Initialize the widget
        AniterWidget.init({
            apiKey: 'demo-key', // This would be a real API key in production
            theme: 'light',
            position: 'bottom-right',
            title: 'Aniter AI Assistant'
        });

        // Theme switching function
        function switchTheme(theme) {
            const container = document.querySelector('.aniter-widget-container');
            if (container) {
                container.className = `aniter-widget-container ${container.className.split(' ')[1]} ${theme}`;
            }
        }
    </script>
</body>

</html>